<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Factory\Flow\Extractor;

use InvalidArgumentException;
use LoginAutonom\EtlBundle\DTO\ExtractorFactoryParams;
use LoginAutonom\EtlBundle\Interfaces\EtlAPIBasedPagerAwareProviderInterface;
use LoginAutonom\EtlBundle\Interfaces\FlowExtractorFactoryInterface;
use ProviderAPIBasedPagerExtractor;
use Symfony\Component\DependencyInjection\Attribute\TaggedLocator;
use Symfony\Contracts\Service\ServiceProviderInterface;

final readonly class ProviderAPIBasedPagerExtractorFactory implements FlowExtractorFactoryInterface
{
    public const PROVIDER = 'provider';
    public const ROW_LIMIT = 'rowLimit';

    public function __construct(
        #[TaggedLocator(EtlAPIBasedPagerAwareProviderInterface::TAG, defaultIndexMethod: 'getName')]
        private ServiceProviderInterface $providers,
    ) {
    }

    public function build(ExtractorFactoryParams $factoryParams): object
    {
        $config = $factoryParams->getConfig();
        $rowLimit = $config[self::ROW_LIMIT] ?? 1000;
        $providerName = $config[self::PROVIDER];

        $this->validateProvider($providerName);

        $provider = $this->providers->get($providerName);

        return new ProviderAPIBasedPagerExtractor(
            rowLimit: $rowLimit,
            config: $config,
            provider: $provider,
        );
    }

    private function validateProvider(string $providerName): void
    {
        if (!$this->providers->has($providerName)) {
            throw new InvalidArgumentException('Provider not exists with this name: ' . $providerName);
        }
    }

    public static function getName(): string
    {
        return 'provider-api-based-extractor';
    }
}
