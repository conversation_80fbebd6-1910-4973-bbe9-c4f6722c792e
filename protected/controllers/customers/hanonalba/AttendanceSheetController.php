<?php

Yang::import('application.components.wfm.AttendanceSheet.controllers.AttendanceSheetCtrl');

class AttendanceSheetController extends AttendanceSheetCtrl
{
    public function __construct()
    {
        parent::__construct('customers/hanonalba/attendanceSheet');
    }

    protected function G2BInit(): void
    {
        $this->view = 'application.views.customers.hanonalba.attendanceSheet.reportGridContent';
        parent::G2BInit();
        parent::setControllerPageTitleId('page_title_timesheet', 'ttwa-wfm');
    }

    protected function getGridSql(): string
    {
        $weekendRestday = App::getSetting('weekendRestday');
        $attendanceSheetUseCreateHalfHourSql = (bool)App::getSetting('attendanceSheetUseCreateHalfHourSql');
        $SQL = '
			SELECT
				base.employee_contract_id,
				base.fullname,
				base.date,
				base.emp_id,
				base.unit_name,
				base.company_name,
				base.company_org_group1_name,
				base.ec_daily_worktime,
				base.used_daily_worktime,
				base.search_filter_from,
				base.search_filter_to,';
        if (App::getSetting('wfmAttendanceSheetRoundHalfHour') == 1) {
            $SQL .= '
				' . $this->createHalfHourSQL('base.worktime_sum', $attendanceSheetUseCreateHalfHourSql) . ' AS worktime_sum_balancebot,
				' . $this->createHalfHourSQL('base.base_worktime_sum', $attendanceSheetUseCreateHalfHourSql) . ' AS base_worktime_sum,
				' . $this->createHalfHourSQL('base.base_overtime_sum', $attendanceSheetUseCreateHalfHourSql) . ' AS base_overtime_sum,
				' . $this->createHalfHourSQL('dt.dt__base_balance_sum', $attendanceSheetUseCreateHalfHourSql) . ' AS base_balance_sum,

				' . $this->createHalfHourSQL('dt.dt__base_worktime_sum', $attendanceSheetUseCreateHalfHourSql) . ' AS dt__base_worktime_sum,
				' . $this->createHalfHourSQL('dt.dt__base_overtime_sum', $attendanceSheetUseCreateHalfHourSql) . ' AS dt__base_overtime_sum,
				' . $this->createHalfHourSQL('base.schedule_sum', $attendanceSheetUseCreateHalfHourSql) . ' AS schedule_sum,
				' . $this->createHalfHourSQL('base.base_abstime_sum', $attendanceSheetUseCreateHalfHourSql) . ' AS base_abstime_sum,
				' . $this->createHalfHourSQL(
                    '(base.base_worktime_sum + IF(base.is_state_type_worktime = 1, base.base_abstime_sum, 0) '
                    . (!App::getSetting(
                        'compensatoryday_sum_to_restday'
                    ) ? " - IF(base.wsu_type_of_daytype = 'COMPENSATORYDAY', base.used_daily_worktime, 0)" : '') . ')',
                    $attendanceSheetUseCreateHalfHourSql
                ) . ' AS worktime_comp_sum,
				';
        } else {
            $SQL .= '
				base.worktime_sum AS worktime_sum_balancebot,
				base.base_worktime_sum AS base_worktime_sum,
				base.base_overtime_sum AS base_overtime_sum,
				dt.dt__base_balance_sum AS base_balance_sum,

				dt.dt__base_worktime_sum AS dt__base_worktime_sum,
				dt.dt__base_overtime_sum AS dt__base_overtime_sum,
				base.schedule_sum AS schedule_sum,
				base.base_abstime_sum AS base_abstime_sum,
				(base.base_worktime_sum + IF(base.is_state_type_worktime = 1, base.base_abstime_sum, 0) '
                . (!App::getSetting(
                    'compensatoryday_sum_to_restday'
                ) ? " - IF(base.wsu_type_of_daytype = 'COMPENSATORYDAY', base.used_daily_worktime, 0)" : '') . ') AS worktime_comp_sum,';
        }

        $SQL .= '
				base.is_restday,
				base.schedule_wtde_sum,
				base.schedule_wtdu_sum,
				base.schedule_wtej_sum,
				base.schedule_wtdu1_sum,
				base.schedule_wtdu2_sum,
				base.wtde_sum,
				base.wtdu_sum,
				base.wtej_sum,
				base.wtdu1_sum,
				base.wtdu2_sum,
				base.otde_sum,
				base.otdu_sum,
				base.otej_sum,
				base.otdu1_sum,
				base.otdu2_sum,
				base.otwej_sum,
				base.dt_daily_worktime,
				base.base_worktime_sum + IF(base.is_state_type_worktime = 1, base.base_abstime_sum, 0) AS worktime_sum,
				base.schedule_work_start AS schedule_work_start,
				base.schedule_work_end AS schedule_work_end,
				base.state_type_dict_id,
				base.absence_hour,
				base.full_day_absence,
				base.has_absence,
				base.has_delegacy,
				base.is_holiday,
				base.is_public_holiday,
				base.holiday_dict_id,
				base.work_type,
				base.is_state_type_worktime,
				regs.reg_in,
				regs.reg_out,
				base.wsu_type_of_daytype AS daytype,
				base.`schedule_sum`,
				dt.mnth__total_5_2_schedule_sum_origi,
				IF(base.schedule_work_start < regs.reg_in, 1, 0) AS is_delay,
				IF(base.worktime_sum < base.`schedule_sum` , 1, 0) AS worked_less_than_sched,
				IFNULL(base.beforewt_ot, 0) AS beforewt_ot,
				IFNULL(base.afterwt_ot, 0) AS afterwt_ot,
				base.`schedule_standby_full_time` + base.`schedule_duty_full_time` AS standby,
				base.`schedule_duty_full_time` AS schedule_duty_full_time,
				base.`schedule_standby_full_time` AS schedule_standby_full_time,
				base.`schedule_overtime_full_time` AS schedule_overtime,
				' . ($weekendRestday ?
                'IF(weekend = 1, 1, 0) AS legal_restday'
                : 'IF(base.`5_2_schedule_sum` = 0, 1, 0) AS legal_restday '
            ) . ",
				base.date_filter_from AS frame_from,
				base.date_filter_to AS frame_to,
				base.5_2_schedule_sum,
				base.is_framework,
				base.employee_contract_valid_to,
				base.workgroup_id,
				IF (bfo.override_key = 'worktime_hours_sec', override_value, 0) AS framestart_workhour,
				IF (bfo.override_key = 'calc_balance_real_sum_sec', override_value, 0) AS framestart_balance
			FROM
				`TEMP_employee_base_calc_daily` base
			LEFT JOIN
				`temp_payroll_transfer_employee_calc_payroll_data_dt` dt ON
					dt.`employee_contract_id` = base.`employee_contract_id`
					AND dt.`date` = base.`date`
			LEFT JOIN
				`TEMP_employee_base_calc_daily_regs` regs ON
					regs.`employee_contract_id` = base.`employee_contract_id`
					AND regs.`date` = base.`date`
			LEFT JOIN `balancemgmt_framestart_overrides` bfo ON
					bfo.`employee_contract_id` = base.`employee_contract_id`
				AND bfo.`day` = base.`date`
				AND bfo.`status` = " . STATUS::PUBLISHED . '
			GROUP BY
				base.`employee_contract_id`,
					base.`date`
			ORDER BY
				base.`fullname` ASC,
				base.employee_contract_id ASC,
				base.`date` ASC
		';

        return $SQL;
    }

    protected function getHeaderValues(): array
    {
        return [
            'date' => Dict::getValue('date'),
            'day' => Dict::getValue('day'),
            'daytype' => Dict::getValue('daytype'),
            'work_start' => Dict::getValue('work_start'),
            'work_end' => Dict::getValue('work_end'),
            'worktime' => Dict::getModuleValue('ttwa-base', 'worktime'),
            'overtime_150' => Dict::getModuleValue('ttwa-wfm', 'overtime_type_150'),
            'overtime_200' => Dict::getModuleValue('ttwa-wfm', 'overtime_type_200'),
            'duty_15' => Dict::getModuleValue('ttwa-wfm', 'duty_type_15'),
            'standby' => Dict::getValue('standby'),
            'wage_supplement_50' => Dict::getModuleValue('ttwa-wfm', 'wage_supplement_50'),
            'shift_allowance_30' => Dict::getModuleValue('ttwa-wfm', 'shift_allowance_30'),
            'shift_allowance_40' => Dict::getModuleValue('ttwa-wfm', 'shift_allowance_40'),
            'holiday_allowance' => Dict::getModuleValue('ttwa-wfm', 'holiday_allowance'),
            'absence_hour' => Dict::getValue('absence_hour'),
            'absence' => Dict::getModuleValue('ttwa-base', 'absence'),
            'worktime_month' => Dict::getValue('worktime_month')
        ];
    }

    protected function columns(): array
    {
        return [
            'date' => ['grid' => true, 'export' => true],
            'day' => ['grid' => true, 'export' => true],
            'daytype' => ['grid' => true, 'export' => true],
            'work_start' => ['grid' => true, 'export' => true],
            'work_end' => ['grid' => true, 'export' => true],
            'worktime' => ['grid' => true, 'export' => true],
            'overtime_150' => ['grid' => true, 'export' => true],
            'overtime_200' => ['grid' => true, 'export' => true],
            'duty_15' => ['grid' => true, 'export' => true],
            'standby' => ['grid' => true, 'export' => true],
            'wage_supplement_50' => ['grid' => true, 'export' => true],
            'shift_allowance_30' => ['grid' => true, 'export' => true],
            'shift_allowance_40' => ['grid' => true, 'export' => true],
            'holiday_allowance' => ['grid' => true, 'export' => true],
            'absence_hour' => ['grid' => true, 'export' => true],
            'absence' => ['grid' => true, 'export' => true],
            'worktime_month' => ['grid' => true, 'export' => true]
        ];
    }
}