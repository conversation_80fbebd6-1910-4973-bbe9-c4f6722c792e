<?php

Yang::import('application.components.wfm.TimeCardCalculation.TimeCardCalculation');

class TimeCardCalculationController extends Controller
{
	public function actionIndex()
	{
		$valid_from = requestParam('valid_from');
		$valid_to = requestParam('valid_to');
		$lock = (int)requestParam('lock');
		
		if(!strtotime($valid_from) || !strtotime($valid_to))
		{
			die("valid_from and valid_to have to be good");
		}
		
		$SQL="
			SELECT DISTINCT
				ec.employee_contract_id
			FROM `employee` e
			INNER JOIN `employee_contract` ec ON
					ec.`employee_id`=e.`employee_id`
				AND ec.`status`=".Status::PUBLISHED."
				AND ec.`valid_from`<=IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
				AND IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."')>=e.valid_from
				AND ec.`ec_valid_from`<=IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
				AND IFNULL(ec.`ec_valid_to`, '".App::getSetting("defaultEnd")."')>=e.valid_from
					
				AND ec.`valid_from`<='$valid_to'
				AND IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."')>='$valid_from'
				AND ec.`ec_valid_from`<='$valid_to'
				AND IFNULL(ec.`ec_valid_to`, '".App::getSetting("defaultEnd")."')>='$valid_from'
			WHERE
					e.`status`=".Status::PUBLISHED."
				AND e.`valid_from`<='$valid_to'
				AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')>='$valid_from'
		";
		
		$results = dbFetchAll($SQL);
		
		$employeeContracts=array();
		for($i=0;$i<count($results);++$i)
		{
			$employeeContracts[]=$results[$i]['employee_contract_id'];
		}
		
		echo "TimeCardCalculation  <br/> ".count($employeeContracts)." employee contracts  <br/> $valid_from - $valid_to <br/><br/>";
		flush();
		
		try {
			$timeCardCalculation = new TimeCardCalculation($employeeContracts, $valid_from, $valid_to, \TRUE);
		} catch (Exception $e) {
			die("Error in TimeCardCalculation");
		}

		try {
			$timeCardCalculation->payrollcalculation();
		} catch (Exception $e) {
			die("Error in payrollcalculation");
		}
		
		if($lock)
		{
			echo "<br/>Start to lock possible days<br/>";
			flush();
			
			$lockTypeExeption = (new GetEmployeeCalc())->getLockTypeException();
			$otv_valid = array();
			$otv_valid['valid_from'] = $valid_from;
			$otv_valid['valid_to'] = $valid_to;
			
			$gec = new GetEmployeeCalc();
			$employeeCalc = $gec->get($valid_from, $valid_to, $employeeContracts, "1,2,5,6");
			$getLegalControlOvertime = new GetLegalControlOvertime(NULL, $otv_valid, \FALSE, "workForce",\FALSE,\TRUE,$employeeContracts);
			$getLegalControlMinDailyWorktime = new GetLegalControlMinDailyWorktime(NULL,$otv_valid,\FALSE,"workForce",\TRUE,$employeeContracts);
			
			$filter= "(`day` BETWEEN '$valid_from' AND '$valid_to')";
			$delCalc = $filter;
			
			foreach ($employeeCalc as $ecID => $calcArr) 
			{
				foreach ($calcArr as $date => $calcRows) 
				{
					$SQL = "
						SELECT
								reg.`row_id`
						FROM `employee` e
						LEFT JOIN `employee_contract` ec ON
								ec.`employee_id`=e.`employee_id`
							AND ec.`status`=" . Status::PUBLISHED . "
							AND '$date' BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '" . App::getSetting("defaultEnd") . "')
							AND '$date' BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '" . App::getSetting("defaultEnd") . "')
						LEFT JOIN `employee_card` ecard ON
								ecard.`employee_contract_id`=ec.`employee_contract_id`
							AND ecard.`status`=" . Status::PUBLISHED . "
							AND '$date' BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '" . App::getSetting("defaultEnd") . "')
						LEFT JOIN `employee_calc_used_daytype` ecud ON
								ecud.`employee_contract_id`=ec.`employee_contract_id`
							AND ecud.`day`='$date'
							AND ecud.`status`=" . Status::PUBLISHED . "
						LEFT JOIN `registration` reg ON
								reg.`card`=ecard.`card`
							AND reg.`status` IN (" . Status::DRAFT . "," . Status::DELETE_REQUEST . "," . Status::WAIT_MODIFY . ")
							AND reg.`time` BETWEEN ecud.`reg_filter_from` AND ecud.`reg_filter_to`
						WHERE
								e.`status`=" . Status::PUBLISHED . "
							AND '$date' BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '" . App::getSetting("defaultEnd") . "')
							AND ec.`employee_contract_id`='$ecID'
							AND reg.`row_id` IS NOT NULL
					";

					$regs = dbFetchAll($SQL);

					$haveOvertimeRequest = WorkScheduleOvertime::haveOvertimeRequest($ecID, $date);
					$overtime_violation = $getLegalControlOvertime->getViolationByEcId($ecID);
					$daily_min_worktime_violation = $getLegalControlMinDailyWorktime->getViolationByEcId($ecID);
					
					foreach ($calcRows as $calcRow) 
					{
						if ((!empty($calcRow["employee_calc_error_type_id"]) && (int) $calcRow["employee_calc_error_type_id"] !== 0) || count($regs) > 0 || $haveOvertimeRequest) {
							
							$filter .= " AND IF(`employee_contract_id` = '" . $calcRow["employee_contract_id"] . "' AND `status` != 5, ";
							$filter .= "`day` != '" . $calcRow["day"] . "', 1)";
							
						} elseif (isset($overtime_violation[$calcRow["day"]]) && $overtime_violation[$calcRow["day"]]['display_type'] === LegalControl::DISPLAY_TYPE_ERROR) {
							$filter .= " AND IF(`employee_contract_id` = '" . $calcRow["employee_contract_id"] . "', `day` != '" . $calcRow["day"] . "', 1)";
						} elseif (isset($daily_min_worktime_violation[$calcRow["day"]]) && $daily_min_worktime_violation[$calcRow["day"]]["display_type"] === LegalControl::DISPLAY_TYPE_ERROR) {
							$filter .= " AND IF(`employee_contract_id` = '" . $calcRow["employee_contract_id"] . "', `day` != '" . $calcRow["day"] . "', 1)";
						}
					}
				}
			}
			
			$filterCalc = $filter;
			$filterCalc .= " AND IF(`inside_type_id` LIKE 'balance', `status` IN (2,5), 1)";
			$filterCalc .= " AND IF(`inside_type_id` LIKE 'ot%%', `status` IN (1,2,5), 1)";
			
			$filter .= " AND `status` IN (2,5)";
			
			$filterCalc .= "  AND `inside_type_id` NOT IN('" . implode("','", $lockTypeExeption) . "') ";
			
			$SQL = "UPDATE `employee_calc` SET `status` = ".Status::LOCKED." WHERE $filterCalc";
			dbExecute($SQL);
			

			$SQL = "UPDATE `employee_calc_used_daytype` SET `status` = ".Status::LOCKED." WHERE $filter";
			dbExecute($SQL);

			$SQL = "UPDATE `employee_calc_message` SET `status` = ".Status::LOCKED." WHERE $filter";
			dbExecute($SQL);

			$filter.=" AND `status`!=".Status::DELETED;
			EmployeeExtraHours::model()->updateAll(['status' => Status::LOCKED], $filter);
			WorkScheduleOvertime::model()->updateAll(['status' => Status::LOCKED], $filter);

			$selDel = "DELETE FROM `employee_calc`
						WHERE $delCalc AND `inside_type_id` NOT IN('" . implode("','", $lockTypeExeption) . "') AND ( (status>=1 AND status<5) OR `inside_type_id` is null )
						";
			dbExecute($selDel);
			
			echo "<br/>Finish<br/>";
			flush();
		}
	}
}

?>