<?php
class CostAggregationController extends Grid2Controller
{	
    private $locked = Status::LOCKED;
    private $published = Status::PUBLISHED;

	public function __construct()
	{
		parent::__construct("wfm/costAggregation");
        $this->maxDays = 365;
	}

	protected function G2BInit()
	{
		parent::setControllerPageTitleId("page_title_cost_aggregation");
		parent::setExportFileName(Dict::getValue("page_title_cost_aggregation"));

		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
        $this->LAGridRights->overrideInitRights("export_csv",		true);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
        
        $this->LAGridDB->enableArrMode();
		parent::G2BInit();
	}

	public function search()
	{
		return $this->getPreDefinedSearchFromDb("workForce");
	}

    protected function dataArray($gridID, $filter) 
    {
        $controllerID = $this->getControllerID();        
        $filter["interval"]["valid_from"] = $filter["valid_from"];
        $filter["interval"]["valid_to"] = $filter["valid_to"];

        $gae = new GetActiveEmployees($filter, "workForce", $controllerID, NULL, FALSE, TRUE);
        $activeEmployees = $gae->getEmployees($filter);
        $employeeCostSums = $this->getEmployeeSumCostSQL(array_column($activeEmployees, "employee_contract_id"), $filter);
        $costCenters = $this->getCostCenterNamesSQL($filter);

        $transformEmployeeCostSums = [];
        $result = [];
        
        foreach ($employeeCostSums as $ecs) {
            $transformEmployeeCostSums[$ecs["employee_contract_id"]][] = $ecs;
        }

        foreach ($activeEmployees as $employee) {     
            if (isset($transformEmployeeCostSums[$employee["employee_contract_id"]])) {
                foreach ($transformEmployeeCostSums[$employee["employee_contract_id"]] as $controws) {                      
                    $result[] = [ 
                                    "emp_id"            => $employee["emp_id"],
                                    "hours"             => $controws["hours"],
                                    strtolower(substr($costCenters[$controws["costcenter_id"]],0,1)) === 'u' ? "cost_center_name" : "work_number" => $costCenters[$controws["costcenter_id"]]
                                ];
                }
            }
        }
    
        return $result;
    }

    public function getEmployeeSumCostSQL($employee_contract_ids, $filter) 
    {
        $contract_ids = join("','", $employee_contract_ids);
        $gec = new GetEmployeeCalc();
        $lockTypeExeption = join("','", $gec->getLockTypeException());

        $sql = "
        SELECT 
            empl_cal.`employee_contract_id`,
            empl_cal.`costcenter_id`,
            SEC_TO_TIME_LOGIN_NOSEC(SUM(empl_cal.`value`)) AS hours             
        FROM 
            `employee_calc` AS empl_cal
        WHERE 
            empl_cal.`day` BETWEEN '{$filter["valid_from"]}' AND '{$filter["valid_to"]}'
            AND empl_cal.`employee_contract_id` IN ('{$contract_ids}')
            AND empl_cal.`status` = '{$this->locked}'
            AND empl_cal.`inside_type_id` NOT IN ('{$lockTypeExeption}')
            AND empl_cal.`costcenter_id` IS NOT NULL
        GROUP BY 
            empl_cal.`employee_contract_id`,
            empl_cal.`costcenter_id`;        
        ";

        return dbFetchAll($sql);
    }

    public function getCostCenterNamesSQL($filter)
    {
        $sql ="
        SELECT 
            `cost_center_id`, 
            `cost_center_name`
	    FROM 
            `cost_center` 
        WHERE 
            `valid_from` <= '{$filter["valid_to"]}' 
            AND '{$filter["valid_from"]}' <= `valid_to`
            AND  `status` = '{$this->published}';
        ";

        return dbFetchAll($sql, "cost_center_id", "cost_center_name");
    }

	public function columns()
	{		
		return [
			'emp_id'		        => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'align' => 'center'],
            'cost_center_name'      => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'align' => 'center'],
            'cost'                  => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'align' => 'center'],
            'work_number'           => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'align' => 'center'],
            'hours'                 => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'align' => 'center'],
            'percent'               => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'align' => 'center'],
            'amount'                => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'align' => 'center'],
            'j_code'                => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'align' => 'center'],
            'dimension_3'           => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'align' => 'center'],
            'dimension_4'           => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'align' => 'center'],
            'dimension_5'           => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'align' => 'center']
        ];    
	}

	public function attributeLabels()
	{
		return [		
			'emp_id'		    =>  Dict::getValue("emp_id"),
            'cost_center_name'  =>	Dict::getValue("operating_code"),
            'cost'              =>	Dict::getValue("cost"),
            'work_number'       =>	Dict::getValue("work_number"),
            'hours'             =>	Dict::getValue("hours"),
            'percent'           =>  Dict::getValue("precent"),
            'amount'            =>  Dict::getValue("amount"),
            'j_code'            =>	Dict::getValue("j_code"),
            'dimension_3'       =>	Dict::getValue("dimension_3"),
            'dimension_4'       =>	Dict::getValue("dimension_4"),
            'dimension_5'       =>	Dict::getValue("dimension_5")      
		];
	}

	public function filters() {
        return [
            'accessControl', // perform access control for CRUD operations
        ];
    }

	public function accessRules()
	{
        return
		[
            [
				'allow', // allow authenticated users to access all actions
                'users' => ['@'],
            ],
            [
				'deny',  // deny all users
                'users' => ['*'],
            ],
        ];
	}
}

?>