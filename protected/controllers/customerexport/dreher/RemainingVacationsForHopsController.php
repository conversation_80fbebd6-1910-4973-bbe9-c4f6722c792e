<?php

ini_set('max_execution_time', 3600);
ini_set('memory_limit', '2048M');

use phpseclib3\Exception\UnableToConnectException;
use phpseclib3\Exception\FileNotFoundException;
use phpseclib3\Net\SFTP;

define('DS', DIRECTORY_SEPARATOR);

class RemainingVacationsForHopsController extends Controller
{
	private $published = Status::PUBLISHED;
	private $draft = Status::DRAFT;
	private $defaultEnd;
	private $absenceCalculationHour;
	private $yearBegin;

	public function __construct()
	{
		$this->defaultEnd = App::getSetting('defaultEnd');
		$this->published;
		$this->draft;
		$this->absenceCalculationHour = App::getSetting('absence_calculation_hour');
		$this->yearBegin = date('Y-01-01');
	}

	public function actionIndex()
	{
		$fixAbs = new AbsenceHourCalcController(null, null, 1); //év elejétől mai napig rendbe teszi a szabadság órákat
		$fixAbs->absenceHourUpdate();
		$this->createCSV();
	}

	private function getRemainingVacations()
	{
		$empData = $this->getActiveEmployees();
		$ecIds = array_keys($empData);
		$frames = $this->getEmployeeBaseAbsences($ecIds);
		$useds = $this->getEmployeeAbsences($ecIds);

		$retArr = [];
		foreach($empData AS $ecId => $data)
		{
			if (empty($data['daily_worktime'])) {
				continue;
			}

            $quantity = $frames[$ecId]['quantity'] ?? 0;
			if (!isset($useds[$ecId]['used'])) {
				continue;
			}
			
			$frame = $this->absenceCalculationHour ? $quantity / $data['daily_worktime'] : $quantity;
			$used = $useds[$ecId]['used'] / $data['daily_worktime'];
			$retArr[] = [
				'hops_id'	=> $data['emp_id'],
				'month'		=> (int)date('m') + 1,
				'year'		=> date('Y'),
				'remaining'	=> round($frame - $used),
			];
		}
		return $retArr;
	}

	private function getActiveEmployees()
	{
		$sql = "
			SELECT
				IFNULL(ext.option5, e.emp_id) AS emp_id,
				ec.employee_contract_id,
				ec.daily_worktime
			FROM employee e
			LEFT JOIN employee_ext ext ON
					e.employee_id = ext.employee_id
				AND ext.status = {$this->published}
				AND CURDATE() BETWEEN ext.valid_from AND ext.valid_to
			LEFT JOIN employee_contract ec ON
					e.employee_id = ec.employee_id
				AND ec.status = {$this->published}
				AND CURDATE() BETWEEN ec.valid_from AND ec.valid_to
				AND CURDATE() BETWEEN ec.ec_valid_from AND ec.ec_valid_to
			WHERE e.status = {$this->published}
				AND CURDATE() BETWEEN e.valid_from AND e.valid_to
				AND ec.row_id IS NOT NULL
		";

		$res = dbFetchAll($sql, 'employee_contract_id');
		return $res;
	}

	private function getEmployeeBaseAbsences($ecIds)
	{
		$implodedEcIds = "'".implode("','", $ecIds)."'";

		$quantity = $this->absenceCalculationHour ? 'quantity_hour' : 'quantity';

		$sql = "
			SELECT SUM({$quantity}) AS quantity,
				employee_contract_id
			FROM employee_base_absence
			WHERE employee_contract_id IN ({$implodedEcIds})
				AND status = {$this->published}
				AND CURDATE() BETWEEN valid_from AND valid_to
			GROUP BY employee_contract_id
		";

		$res = dbFetchAll($sql, 'employee_contract_id');
		return $res;
	}

	private function getEmployeeAbsences($ecIds)
	{
		$implodedEcIds = "'".implode("','", $ecIds)."'";

		$sql = "
			SELECT SUM(ea.absence_hour) * COUNT(DISTINCT ea.row_id)/COUNT(ea.absence_hour) AS used,
				ea.employee_contract_id
			FROM employee_absence ea
			LEFT JOIN link_at_to_bat link ON
				link.state_type_id = ea.state_type_id
			WHERE ea.employee_contract_id IN ({$implodedEcIds})
				AND ea.status IN ({$this->draft}, {$this->published})
				AND ea.day BETWEEN '{$this->yearBegin}' AND CURDATE()
				AND link.row_id IS NOT NULL
			GROUP BY ea.employee_contract_id
		";

		$res = dbFetchAll($sql, 'employee_contract_id');
		return $res;
	}

	private function createCSV(): void
    {
		$data = $this->getRemainingVacations();
		$dataCount = count($data);

		if ($dataCount > 0)
		{
			$fileString = 'Unique EE ID;month;year;number of remaining vacations /day/';
			$fileString .= "\r\n";

			foreach ($data as $line)
			{
				$fileString .= implode(';', $line);
				$fileString .= "\n";
			}

			$path = Yang::getBasePath() . DS . '..' . DS . 'webroot' . DS . 'file_storage' . DS . 'dreher_export';
			if (!file_exists($path) && !is_dir($path))
			{
				mkdir($path, 0755, TRUE);
			}
			$filename = $path . DS . "remaining_vacations_" . date('Y-m-d_His') . ".csv";

			$fileString = iconv('UTF-8', 'ISO-8859-1//TRANSLIT', $fileString);

			$f = fopen($filename, 'w');
			fwrite($f, $fileString);
			fclose($f);

			$this->sendCSV($filename);
		}
	}

	protected function sendCSV($file): void
    {
        $SFTPhost = Yang::getParam('SFTPhost');
        $SFTPport = (int)Yang::getParam('SFTPport');
        $SFTPusername = Yang::getParam('SFTPusername');
        $SFTPpassword = Yang::getParam('SFTPpassword');
        $SFTPremoteDir = Yang::getParam('SFTPremoteDir');
        $fileName = 'remaining_vacations_' . date('Y-m-d_His') . '.csv';

		try {
			$sftp = $this->connectToSFTP($SFTPhost, $SFTPport, $SFTPusername, $SFTPpassword);
			$this->uploadFileToSFTP($sftp, $SFTPremoteDir, $fileName, $file);
			$sftp->disconnect();
		} catch (UnableToConnectException $e) {
			Yang::log('SFTP Connection Error: ' . $e->getMessage(), 'log', 'Ease - Hops remaining vacations export');
		} catch (FileNotFoundException $e) {
			Yang::log('SFTP File Upload Error: ' . $e->getMessage(), 'log', 'Ease - Hops remaining vacations export');
		} catch (\Exception $e) {
			Yang::log('Unexpected Error: ' . $e->getMessage(), 'log', 'Ease - Hops remaining vacations export');
		}
	}

	private function connectToSFTP(string $host, int $port, string $username, string $password): SFTP
    {
		$sftp = new SFTP($host, $port);
		
		if (!$sftp->login($username, $password)) {
			throw new UnableToConnectException("Login failed to $host");
		}
		
		return $sftp;
	}

	private function uploadFileToSFTP(SFTP $sftp, string $remoteDir, string $fileName, string $localFile): void
    {
		if (!$sftp->put(DS . $remoteDir . DS . $fileName, $localFile, SFTP::SOURCE_LOCAL_FILE)) {
			throw new FileNotFoundException('Failed to send ' . $fileName . ' Error: ' . $sftp->getLastSFTPError());
		}
	}
} 