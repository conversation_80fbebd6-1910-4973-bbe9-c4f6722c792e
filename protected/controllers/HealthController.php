<?php

declare(strict_types=1);

final class HealthController extends Controller
{
    public const OK = 'ok';
    public const ERROR = 'error';
    public const DATABASE = 'database';
    public const STATUS = 'status';
    public const TIMESTAMP = 'timestamp';
    public const VERSION = 'version';
    public const CHECKS = 'checks';

    public function actionIndex()
    {
        $checks = [];
        $overallStatus = self::OK;
        $version = "UNKNOWN";
        try {
            dbExecute('SELECT 1');
            $checks[self::DATABASE] = self::OK;
        } catch (\Throwable $th) {
            $overallStatus = self::ERROR;
            $checks[self::DATABASE] = self::ERROR;
        }

        $metadataPath = dirname(__DIR__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . '..'
            . DIRECTORY_SEPARATOR . 'metadata.json';
        
        if (is_file($metadataPath)) {
            try {
                $metadata = json_decode(
                    file_get_contents($metadataPath),
                    true,
                    flags: JSON_THROW_ON_ERROR
                );
                $version = $metadata['BUILD_NUMBER'];
            } catch (\Exception $e) {
            }
        }

        $response = [
            self::STATUS => $overallStatus,
            self::TIMESTAMP => date('c'),
            self::VERSION => $version,
            self::CHECKS => $checks,
        ];

        echo json_encode($response);
    }
}
