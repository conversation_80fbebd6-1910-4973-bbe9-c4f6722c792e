<?php

/**
 * File Upload
 */
class FileUploadController extends Controller
{
	/**
	 * 
	 * @return $_SESSION - file[name], file[path]
	 */
	public function actionUpload() {
		$filetmp =$_FILES['file']['tmp_name'];
		$filename = $_FILES['file']['name'];
		move_uploaded_file($filetmp,Yang::getBasePath()."/../webroot/upload/file/".$filename);
		$new_filename = md5(microtime()) . ".xlsx";
		rename(Yang::getBasePath()."/../webroot/upload/file/".$filename, Yang::getBasePath()."/../webroot/upload/file/".$new_filename);
		$_SESSION['file']['name'] = $new_filename;
		$_SESSION['file']['path'] = Yang::getBasePath()."/../webroot/upload/file/";
	
		Yang::log($filename . " -> " . $new_filename, 'info', 'FileUpload');
		
		echo json_encode(['status' => 'success','filename' => $filename]);
	}
}

?>